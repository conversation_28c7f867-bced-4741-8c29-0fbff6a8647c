<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Certificate Renamer</title>
    <link rel="stylesheet" href="index.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script type="importmap">
{
  "imports": {
    "@google/genai": "https://esm.sh/@google/genai@^0.15.0",
    "pdfjs-dist/build/pdf": "https://esm.sh/pdfjs-dist@4.4.168/build/pdf.mjs",
    "pdfjs-dist/": "https://esm.sh/pdfjs-dist@^5.3.93/"
  }
}
</script>
  <link rel="stylesheet" href="/index.css">
</head>
  <body>
    <main>
      <header>
        <h1>AI Certificate Renamer</h1>
        <p>
          Upload certificate images or PDFs, and AI will suggest new names based
          on their content.
        </p>
      </header>

      <div class="card">
        <div id="upload-container">
          <input
            type="file"
            id="file-input"
            accept="image/*,application/pdf"
            hidden
            multiple
          />
          <label for="file-input" id="upload-label">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="17 8 12 3 7 8" />
              <line x1="12" x2="12" y1="3" y2="15" />
            </svg>
            <span>Click to upload or drag & drop</span>
            <small>Images and PDFs are supported</small>
          </label>
        </div>

        <div id="result-view" class="hidden">
          <div id="results-list">
            <!-- Results will be dynamically inserted here -->
          </div>
          <button id="reset-button" class="button">Process More Files</button>
        </div>
      </div>
    </main>

    <template id="result-item-template">
      <div class="result-item">
        <img class="result-preview" src="#" alt="File preview" />
        <div class="result-info">
          <p><strong>Original:</strong> <span class="original-filename"></span></p>
          <p><strong>Suggested:</strong> <span class="new-filename"></span></p>
        </div>
        <div class="result-status">
          <div class="loader"></div>
          <a href="#" class="button download-link hidden" download="">Download</a>
        </div>
      </div>
    </template>

    <script type="module" src="index.tsx"></script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
