/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import { GoogleGenAI } from '@google/genai';
import * as pdfjsLib from 'pdfjs-dist/build/pdf';

// Set the worker source for pdf.js
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://esm.sh/pdfjs-dist@4.4.168/build/pdf.worker.mjs`;

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

// DOM Elements
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const uploadContainer = document.getElementById(
  'upload-container'
) as HTMLDivElement;
const uploadLabel = document.getElementById('upload-label') as HTMLLabelElement;
const resultView = document.getElementById('result-view') as HTMLDivElement;
const resultsList = document.getElementById('results-list') as HTMLDivElement;
const resetButton = document.getElementById('reset-button') as HTMLButtonElement;
const resultItemTemplate = document.getElementById(
  'result-item-template'
) as HTMLTemplateElement;

// Event Listeners
fileInput.addEventListener('change', (event) => {
  const files = (event.target as HTMLInputElement).files;
  if (files && files.length > 0) {
    handleFiles(files);
  }
});

uploadLabel.addEventListener('dragover', (event) => {
  event.preventDefault();
  uploadLabel.classList.add('dragover');
});

uploadLabel.addEventListener('dragleave', () => {
  uploadLabel.classList.remove('dragover');
});

uploadLabel.addEventListener('drop', (event) => {
  event.preventDefault();
  uploadLabel.classList.remove('dragover');
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    fileInput.files = files;
    handleFiles(files);
  }
});

resetButton.addEventListener('click', () => {
  resultView.classList.add('hidden');
  uploadContainer.classList.remove('hidden');
  resultsList.innerHTML = '';
  fileInput.value = ''; // Reset file input
});

async function handleFiles(files: FileList) {
  uploadContainer.classList.add('hidden');
  resultView.classList.remove('hidden');
  resultsList.innerHTML = '';

  const filePromises = Array.from(files).map((file) => processFile(file));
  await Promise.all(filePromises);
}

async function processFile(file: File) {
  const templateClone = resultItemTemplate.content.cloneNode(
    true
  ) as DocumentFragment;
  const resultItem = templateClone.querySelector('.result-item') as HTMLDivElement;
  const preview = templateClone.querySelector(
    '.result-preview'
  ) as HTMLImageElement;
  const originalFilenameSpan = templateClone.querySelector(
    '.original-filename'
  ) as HTMLSpanElement;
  const newFilenameSpan = templateClone.querySelector(
    '.new-filename'
  ) as HTMLSpanElement;
  const downloadLink = templateClone.querySelector(
    '.download-link'
  ) as HTMLAnchorElement;
  const loader = templateClone.querySelector('.loader') as HTMLDivElement;

  resultsList.appendChild(templateClone);

  originalFilenameSpan.textContent = file.name;
  newFilenameSpan.textContent = 'Analyzing...';
  const objectURL = URL.createObjectURL(file);

  try {
    const { base64Data, mimeType, previewUrl } = await convertFileToImageBase64(
      file
    );
    preview.src = previewUrl;

    const imagePart = {
      inlineData: { mimeType, data: base64Data },
    };
    const textPart = {
      text: 'Read this certificate and extract the full name of the person it was awarded to. Respond with only the name and nothing else.',
    };

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: { parts: [imagePart, textPart] },
    });

    const newName = response.text.trim();
    if (!newName) {
      throw new Error('AI could not determine a name.');
    }

    const extension = file.name.split('.').pop() || 'png';
    const sanitizedName = newName
      .replace(/[^a-z0-9\s-]/gi, '')
      .replace(/\s+/g, '_');
    const finalFilename = `${sanitizedName}.${extension}`;

    newFilenameSpan.textContent = finalFilename;
    downloadLink.href = objectURL;
    downloadLink.download = finalFilename;
    downloadLink.classList.remove('hidden');
  } catch (error) {
    console.error(`Error processing file ${file.name}:`, error);
    newFilenameSpan.textContent = `Error: Could not process file.`;
    if (error instanceof Error) {
        newFilenameSpan.textContent += ` ${error.message}`;
    }
    resultItem.classList.add('error');
  } finally {
    loader.classList.add('hidden');
  }
}

async function convertFileToImageBase64(
  file: File
): Promise<{ base64Data: string; mimeType: string; previewUrl: string }> {
  if (file.type.startsWith('image/')) {
    const previewUrl = URL.createObjectURL(file);
    const base64String = await fileToBase64(file);
    const [header, data] = base64String.split(',');
    if (!header || !data) {
      throw new Error('Invalid image format');
    }
    const mimeType = header.split(':')[1].split(';')[0];
    return { base64Data: data, mimeType, previewUrl };
  } else if (file.type === 'application/pdf') {
    const fileUrl = URL.createObjectURL(file);
    const pdf = await pdfjsLib.getDocument(fileUrl).promise;
    const page = await pdf.getPage(1);
    const viewport = page.getViewport({ scale: 1.5 });

    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) {
      throw new Error('Could not create canvas context');
    }
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    await page.render({ canvasContext: context, viewport: viewport }).promise;

    // The previewUrl is the canvas data url, but the original fileUrl is used for download
    const dataUrl = canvas.toDataURL('image/jpeg');
    const [header, data] = dataUrl.split(',');
    if (!data) {
      throw new Error('Could not convert PDF page to image');
    }

    return { base64Data: data, mimeType: 'image/jpeg', previewUrl: dataUrl };
  } else {
    throw new Error(`Unsupported file type: ${file.type}`);
  }
}

function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
}
