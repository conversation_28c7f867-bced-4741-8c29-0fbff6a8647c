:root {
  --background-color: #f8f9fa;
  --text-color: #212529;
  --primary-color: #007bff;
  --primary-hover-color: #0056b3;
  --card-background: #ffffff;
  --card-border: #dee2e6;
  --drop-zone-border: #ced4da;
  --drop-zone-hover-bg: #e9ecef;
  --success-color: #28a745;
  --error-color: #dc3545;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
}

main {
  width: 100%;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

header {
  text-align: center;
}

h1 {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

.card {
  background-color: var(--card-background);
  border-radius: 8px;
  border: 1px solid var(--card-border);
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

#upload-container {
  width: 100%;
}

#upload-label {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 3rem;
  border: 2px dashed var(--drop-zone-border);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  text-align: center;
}

#upload-label.dragover,
#upload-label:hover {
  background-color: var(--drop-zone-hover-bg);
  border-color: var(--primary-color);
}

#upload-label svg {
  color: var(--primary-color);
  width: 32px;
  height: 32px;
}

#upload-label span {
  font-weight: 500;
  color: #495057;
}

#upload-label small {
  color: #6c757d;
  font-size: 0.875rem;
}

#result-view {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

#results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--card-border);
  border-radius: 8px;
  background-color: #fdfdfd;
}

.result-preview {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  background-color: #f1f3f5;
}

.result-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  word-break: break-all;
  color: var(--text-color);
}

.result-info strong {
  color: #495057;
}

.result-status {
  width: 100px;
  text-align: right;
}

.button {
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  padding: 0.6rem 1.2rem;
  border-radius: 5px;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  font-size: 0.875rem;
  display: inline-block;
}

.button:hover {
  background-color: var(--primary-hover-color);
}

#reset-button {
  align-self: center;
  margin-top: 1rem;
}

.hidden {
  display: none !important;
}

.loader {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(0, 123, 255, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  vertical-align: middle;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
